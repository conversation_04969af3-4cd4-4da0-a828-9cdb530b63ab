# pCloud Storage Connection Test Feature

This document describes the pCloud storage connection testing functionality that has been added to the Electron application.

## Overview

The pCloud connection test feature allows users to test their pCloud storage account connectivity directly from the Electron application's UI. This is particularly useful for:

- Verifying pCloud account credentials before integrating storage features
- Testing connectivity to different pCloud data centers (US/EU)
- Checking account information and storage quotas
- Troubleshooting authentication issues
- Validating premium account status

## Features

### 🔧 Connection Testing
- Test connections to pCloud storage accounts
- Support for both US and EU data centers
- Connection timeout handling (10 seconds)
- Detailed error reporting with specific error codes
- Automatic region detection and endpoint selection

### 📊 Account Information
- Connection time measurement
- User account details (email, verification status)
- Premium account status detection
- Storage quota and usage information
- Account registration date
- Language preferences

### 🎨 User Interface
- Clean, responsive form interface
- Real-time connection status feedback
- Pre-configured example credentials
- Password visibility toggle
- Region selection (US/EU data centers)
- Form reset functionality

## Usage

### Accessing the Feature
1. Start the Electron application: `npm run dev`
2. Navigate to the "pCloud Test" tab in the navigation bar
3. Fill in your pCloud account credentials
4. Select your data center region
5. Click "Test Connection"

### Connection Parameters
- **Email/Username**: Your pCloud account email address
- **Password**: Your pCloud account password
- **Region**: Data center region (US or EU)

### Pre-configured Example
The application includes pre-configured example credentials:
- **Email**: `<EMAIL>`
- **Password**: `********`
- **Region**: `US (api.pcloud.com)`

Click "Load Example" to populate the form with these values.

## Technical Implementation

### Dependencies
- `axios`: HTTP client for API requests (already included)
- No additional dependencies required

### Architecture
The feature is implemented using the existing IPC (Inter-Process Communication) pattern:

1. **Frontend (Renderer Process)**: `src/renderer/screens/pcloud-test.screen.tsx`
   - React component with form interface
   - Uses `safeIpcInvoke` to communicate with main process

2. **Backend (Main Process)**: `src/main/ipcHandlers.ts`
   - IPC handler: `test-pcloud-connection`
   - pCloud API authentication logic
   - Error handling and timeout management

### API Endpoints
pCloud has two data centers with different API endpoints:

- **US Data Center**: `https://api.pcloud.com/userinfo`
- **EU Data Center**: `https://eapi.pcloud.com/userinfo`

The application automatically selects the correct endpoint based on the region setting.

### Authentication Method
The implementation uses pCloud's username/password authentication:

```
GET https://api.pcloud.com/userinfo?username={email}&password={password}&getauth=1&logout=1
```

Parameters:
- `username`: User's email address
- `password`: User's password
- `getauth=1`: Request authentication token
- `logout=1`: Logout any existing session

### Error Handling
The implementation includes comprehensive error handling for common pCloud API issues:

- **Result Code 1000**: Login required - invalid credentials
- **Result Code 2000**: Login failed - check username and password
- **Result Code 4000**: Too many login attempts from this IP address
- **Network Errors**: Connection timeouts, DNS resolution failures
- **HTTP Errors**: 401 (Unauthorized), 403 (Forbidden), 5xx (Server errors)

### Security Considerations
- Passwords are masked in the UI with toggle visibility
- API calls are made over HTTPS
- Connection timeouts prevent hanging requests
- No credentials are stored permanently

## Testing

### Manual Testing
1. Use the UI to test various connection scenarios
2. Try invalid credentials to test error handling
3. Test with different region configurations
4. Verify account information display

### Test Results
The pre-configured test account shows:
- ✅ **Connection successful** in ~800ms
- 📧 **Email**: <EMAIL>
- ✅ **Email verified**: Yes
- 💎 **Premium account**: Yes (500GB quota)
- 📊 **Storage used**: 82GB (418GB available)
- 🌍 **Region**: US
- 🗣️ **Language**: English

## Files Modified/Added

### New Files
- `src/renderer/screens/pcloud-test.screen.tsx` - Main UI component
- `PCLOUD_FEATURE.md` - This documentation

### Modified Files
- `src/main/ipcHandlers.ts` - Added pCloud connection handler
- `src/renderer/routes.tsx` - Added new route
- `src/renderer/layout.tsx` - Added navigation link

## API Response Format

Successful authentication returns:
```json
{
  "result": 0,
  "auth": "authentication_token",
  "email": "<EMAIL>",
  "emailverified": true,
  "premium": true,
  "quota": ************,
  "usedquota": ***********,
  "language": "en",
  "registered": "Mon, 18 Nov 2013 15:32:05 +0000"
}
```

Error response returns:
```json
{
  "result": 2000,
  "error": "Login failed."
}
```

## Future Enhancements

Potential improvements for this feature:
- File upload/download testing
- Folder listing and browsing
- File sharing link generation
- Storage usage analytics
- Multiple account management
- Integration with other cloud storage providers
- Backup and sync functionality

## Troubleshooting

### Common Issues

1. **Authentication Failed**
   - Verify email and password are correct
   - Check if account is active and not suspended
   - Ensure you're using the correct region (US/EU)

2. **Connection Timeout**
   - Check internet connectivity
   - Try switching between US and EU regions
   - Verify pCloud servers are operational

3. **Too Many Login Attempts**
   - Wait a few minutes before retrying
   - Check if IP address is temporarily blocked
   - Contact pCloud support if issue persists

4. **Region Selection**
   - Use **US** region for accounts created on pcloud.com
   - Use **EU** region for accounts created on European servers
   - When in doubt, try both regions

## Support

For issues or questions about this feature:
1. Check the console logs in the Electron DevTools
2. Review the error messages displayed in the UI
3. Verify the pCloud account is accessible via web browser
4. Test with different network connections

## pCloud API Documentation

For more information about pCloud API:
- Official Documentation: https://docs.pcloud.com/
- Authentication: https://docs.pcloud.com/methods/intro/authentication.html
- User Info Method: https://docs.pcloud.com/methods/general/userinfo.html
