import { defineConfig, externalizeDepsPlugin } from 'electron-vite'
import tsconfigPathsPlugin from 'vite-tsconfig-paths'
import react from '@vitejs/plugin-react'
import tailwindcss from 'tailwindcss'
import { resolve } from 'path'

import { settings } from './src/lib/electron-router-dom'

const tsconfigPaths = tsconfigPathsPlugin({
  projects: [resolve('tsconfig.json')],
})

export default defineConfig({
  main: {
    plugins: [tsconfigPaths, externalizeDepsPlugin()],
    build: {
      minify: 'esbuild',
      rollupOptions: {
        external: ['electron']
      }
    },
    esbuild: {
      drop: process.env.NODE_ENV === 'production' ? ['console', 'debugger'] : []
    }
  },

  renderer: {
    plugins: [tsconfigPaths, react()],

    css: {
      postcss: {
        plugins: [
          tailwindcss({
            config: './tailwind.config.ts',
          }),
        ],
      },
    },

    build: {
      minify: 'esbuild',
      rollupOptions: {
        output: {
          manualChunks: undefined
        }
      }
    },

    esbuild: {
      drop: process.env.NODE_ENV === 'production' ? ['console', 'debugger'] : []
    },

    server: {
      port: settings.port,
    },
  },
})
