# Bank Master Pagination Feature

This document describes the pagination functionality that has been added to the Bank Master management system.

## Overview

The pagination feature enhances the Bank Master interface by providing efficient data loading, search capabilities, and sorting options. This is essential for managing large datasets and improving user experience.

## Features

### 📄 **Pagination Controls**
- **Page Navigation**: First, Previous, Next, Last buttons
- **Page Numbers**: Clickable page number buttons (shows up to 5 pages)
- **Page Size Selection**: 5, 10, 20, or 50 records per page
- **Smart Page Display**: Shows relevant page numbers based on current position

### 🔍 **Search Functionality**
- **Real-time Search**: Search by bank code or bank name
- **Case-insensitive**: Searches work regardless of case
- **Instant Results**: Results update as you type
- **Search Reset**: Pagination resets to page 1 when searching

### 🔄 **Sorting Capabilities**
- **Sortable Columns**: ID, Bank Code, Bank Name, Status, Created Date, Updated Date
- **Sort Direction**: Ascending (↑) or Descending (↓) indicators
- **Visual Feedback**: Hover effects and sort indicators
- **Persistent Sorting**: Sort order maintained during pagination

### 📊 **Information Display**
- **Record Count**: Shows current range and total records
- **Filter Status**: Displays active search terms
- **Status Summary**: Active/Inactive bank counts
- **Loading States**: Visual feedback during data loading

## Technical Implementation

### Backend Changes (`src/main/ipcHandlers.ts`)

#### New Interfaces
```typescript
interface PaginationParams {
  page?: number;
  pageSize?: number;
  search?: string;
  sortBy?: string;
  sortOrder?: 'ASC' | 'DESC';
}

interface BankMasterResponse {
  success: boolean;
  message: string;
  data?: BankMaster | BankMaster[];
  pagination?: {
    currentPage: number;
    totalPages: number;
    totalRecords: number;
    pageSize: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
  error?: string;
}
```

#### Enhanced get-banks Handler
- **Pagination Logic**: LIMIT and OFFSET for efficient data retrieval
- **Search Implementation**: LIKE queries on bank code and name
- **Sorting Support**: Dynamic ORDER BY with validation
- **Count Queries**: Separate query for total record count
- **Parameter Validation**: Safe handling of sort columns and directions

### Frontend Changes (`src/renderer/screens/bank-master.screen.tsx`)

#### New State Management
```typescript
// Pagination state
const [currentPage, setCurrentPage] = useState(1);
const [pageSize, setPageSize] = useState(10);
const [totalPages, setTotalPages] = useState(0);
const [totalRecords, setTotalRecords] = useState(0);
const [hasNextPage, setHasNextPage] = useState(false);
const [hasPreviousPage, setHasPreviousPage] = useState(false);

// Search and sort state
const [searchTerm, setSearchTerm] = useState('');
const [sortBy, setSortBy] = useState('ftbnkname');
const [sortOrder, setSortOrder] = useState<'ASC' | 'DESC'>('ASC');
```

#### New Functions
- **handlePageChange**: Navigate to specific page
- **handlePageSizeChange**: Change number of records per page
- **handleSearch**: Filter records by search term
- **handleSort**: Sort by column with direction toggle

## User Interface Components

### Search Bar
```tsx
<input
  type="text"
  placeholder="Search by bank code or name..."
  value={searchTerm}
  onChange={(e) => handleSearch(e.target.value)}
  className="w-full px-4 py-2 border border-gray-300 rounded-md"
/>
```

### Page Size Selector
```tsx
<select
  value={pageSize}
  onChange={(e) => handlePageSizeChange(parseInt(e.target.value))}
>
  <option value={5}>5</option>
  <option value={10}>10</option>
  <option value={20}>20</option>
  <option value={50}>50</option>
</select>
```

### Sortable Column Headers
```tsx
<th 
  className="cursor-pointer hover:bg-gray-100"
  onClick={() => handleSort('ftbnkname')}
>
  <div className="flex items-center gap-1">
    Bank Name
    {sortBy === 'ftbnkname' && (
      <span className="text-xs">
        {sortOrder === 'ASC' ? '↑' : '↓'}
      </span>
    )}
  </div>
</th>
```

### Pagination Controls
- **Navigation Buttons**: First, Previous, Next, Last
- **Page Numbers**: Dynamic page number display
- **Record Information**: "Showing X to Y of Z records"

## Performance Benefits

### Database Efficiency
- **Reduced Data Transfer**: Only loads required records
- **Optimized Queries**: Uses LIMIT/OFFSET for efficient pagination
- **Indexed Sorting**: Leverages database indexes for fast sorting
- **Separate Count Queries**: Efficient total record counting

### User Experience
- **Faster Loading**: Smaller datasets load quickly
- **Responsive Interface**: Immediate feedback on interactions
- **Memory Efficiency**: Reduced client-side memory usage
- **Smooth Navigation**: Intuitive pagination controls

## Usage Examples

### Basic Pagination
1. **Default View**: Shows first 10 records sorted by bank name
2. **Page Navigation**: Click page numbers or navigation buttons
3. **Page Size**: Change from dropdown (5, 10, 20, 50)

### Search Operations
1. **Search by Code**: Type "BBL" to find Bangkok Bank
2. **Search by Name**: Type "Bangkok" to find related banks
3. **Clear Search**: Empty search field to show all records

### Sorting Operations
1. **Sort by Name**: Click "Bank Name" header
2. **Reverse Sort**: Click same header again for descending order
3. **Sort by Status**: Click "Status" to group active/inactive banks
4. **Sort by Date**: Click date columns for chronological ordering

## Test Results

The pagination system has been tested with:
- ✅ **49 total banks** in the database
- ✅ **Page sizes**: 5, 10, 20, 50 records per page
- ✅ **Search functionality**: Case-insensitive search working
- ✅ **Sorting**: All columns sortable in both directions
- ✅ **Edge cases**: Empty results, last page, large datasets
- ✅ **Performance**: Fast loading with LIMIT/OFFSET queries

## Configuration

### Default Settings
- **Page Size**: 10 records per page
- **Sort Column**: Bank Name (ftbnkname)
- **Sort Order**: Ascending (ASC)
- **Search**: Empty (shows all records)

### Customizable Options
- **Page Sizes**: 5, 10, 20, 50 (easily configurable)
- **Sort Columns**: All major columns available
- **Search Fields**: Bank code and bank name
- **Page Display**: Shows up to 5 page numbers

## Future Enhancements

Potential improvements for the pagination system:
- **Advanced Filters**: Status, date range, created by filters
- **Export Options**: Export current page or all filtered results
- **Bulk Operations**: Select multiple records across pages
- **Column Customization**: Show/hide columns
- **Saved Searches**: Save frequently used search criteria
- **Keyboard Navigation**: Arrow keys for page navigation
- **URL State**: Maintain pagination state in URL for bookmarking

## Troubleshooting

### Common Issues

1. **Slow Loading**
   - Check database connection
   - Verify indexes on sort columns
   - Consider reducing page size

2. **Search Not Working**
   - Verify search term format
   - Check for special characters
   - Ensure database connection

3. **Pagination Errors**
   - Check total record count
   - Verify page calculations
   - Ensure valid page numbers

4. **Sort Issues**
   - Verify column names in sort function
   - Check sort direction logic
   - Ensure database supports sorting

## Integration Notes

The pagination system integrates seamlessly with:
- **CRUD Operations**: Create, edit, delete maintain pagination state
- **Real-time Updates**: Automatic refresh after data changes
- **Error Handling**: Graceful handling of connection issues
- **Loading States**: Visual feedback during operations

This pagination implementation provides a professional-grade data management interface suitable for production use with large datasets.
