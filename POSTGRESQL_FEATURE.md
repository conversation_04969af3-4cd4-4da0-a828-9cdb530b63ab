# PostgreSQL Connection Test Feature

This document describes the PostgreSQL connection testing functionality that has been added to the Electron application.

## Overview

The PostgreSQL connection test feature allows users to test database connections directly from the Electron application's UI. This is particularly useful for:

- Verifying database connectivity before deploying applications
- Testing different PostgreSQL configurations
- Troubleshooting connection issues
- Validating credentials and SSL settings

## Features

### 🔧 Connection Testing
- Test connections to any PostgreSQL database
- Support for SSL/TLS connections
- Connection timeout handling (10 seconds)
- Query timeout handling (5 seconds)
- Detailed error reporting with specific error codes

### 📊 Connection Information
- Connection time measurement
- PostgreSQL server version detection
- Detailed error messages for common issues
- Connection string display (with masked password)

### 🎨 User Interface
- Clean, responsive form interface
- Real-time connection status feedback
- Pre-configured example for Neon database
- Password visibility toggle
- Form reset functionality

## Usage

### Accessing the Feature
1. Start the Electron application: `npm run dev`
2. Navigate to the "PostgreSQL Test" tab in the navigation bar
3. Fill in your database connection details
4. Click "Test Connection"

### Connection Parameters
- **Host**: Database server hostname or IP address
- **Port**: Database port (default: 5432)
- **Database**: Database name to connect to
- **Username**: Database user credentials
- **Password**: Database password
- **SSL**: Enable/disable SSL connection

### Pre-configured Example
The application includes a pre-configured example for the Neon PostgreSQL database:
- Host: `ep-dawn-fog-a1jk7z7f-pooler.ap-southeast-1.aws.neon.tech`
- Port: `5432`
- Database: `neondb`
- User: `neondb_owner`
- Password: `npg_v1aKnJdNXif4`
- SSL: `enabled`

Click "Load Neon Example" to populate the form with these values.

## Technical Implementation

### Dependencies
- `pg`: PostgreSQL client for Node.js
- `@types/pg`: TypeScript definitions for pg

### Architecture
The feature is implemented using the existing IPC (Inter-Process Communication) pattern:

1. **Frontend (Renderer Process)**: `src/renderer/screens/postgresql-test.screen.tsx`
   - React component with form interface
   - Uses `safeIpcInvoke` to communicate with main process

2. **Backend (Main Process)**: `src/main/ipcHandlers.ts`
   - IPC handler: `test-postgresql-connection`
   - PostgreSQL client connection logic
   - Error handling and timeout management

### Error Handling
The implementation includes comprehensive error handling for common PostgreSQL connection issues:

- `ECONNREFUSED`: Server down or unreachable
- `ENOTFOUND`: Invalid hostname/IP address
- `ECONNRESET`: Network connectivity issues
- `28P01`: Authentication failed
- `3D000`: Database does not exist
- `28000`: Invalid authorization
- Timeout errors: Connection or query timeouts

### Security Considerations
- Passwords are masked in the connection string display
- SSL connections are supported with `rejectUnauthorized: false` for development
- Connection timeouts prevent hanging connections
- Connections are properly closed after testing

## Testing

### Manual Testing
1. Use the UI to test various connection scenarios
2. Try invalid credentials to test error handling
3. Test with different SSL configurations

### Automated Testing
A standalone test script is provided: `test-postgresql-connection.js`

Run the test:
```bash
node test-postgresql-connection.js
```

This script tests the core connection logic independently of the Electron application.

## Files Modified/Added

### New Files
- `src/renderer/screens/postgresql-test.screen.tsx` - Main UI component
- `test-postgresql-connection.js` - Standalone test script
- `POSTGRESQL_FEATURE.md` - This documentation

### Modified Files
- `src/main/ipcHandlers.ts` - Added PostgreSQL connection handler
- `src/renderer/routes.tsx` - Added new route
- `src/renderer/layout.tsx` - Added navigation link
- `package.json` - Added pg and @types/pg dependencies

## Connection String Format

The application displays the connection string in the standard PostgreSQL format:
```
postgresql://username:password@host:port/database?sslmode=require
```

Note: The password is masked with `***` in the display for security.

## Future Enhancements

Potential improvements for this feature:
- Save/load connection profiles
- Connection pooling testing
- Query execution interface
- Database schema browsing
- Export connection test results
- Integration with other database types (MySQL, MongoDB, etc.)

## Troubleshooting

### Common Issues

1. **Connection Timeout**
   - Check if the database server is running
   - Verify network connectivity
   - Ensure firewall allows connections on the specified port

2. **Authentication Failed**
   - Verify username and password
   - Check if the user has permission to access the database
   - Ensure the user is allowed to connect from your IP address

3. **SSL Connection Issues**
   - Try toggling the SSL option
   - For development, SSL is configured with `rejectUnauthorized: false`
   - For production, proper SSL certificates should be used

4. **Database Not Found**
   - Verify the database name is correct
   - Ensure the database exists on the server
   - Check if the user has access to the specified database

## Support

For issues or questions about this feature:
1. Check the console logs in the Electron DevTools
2. Review the error messages displayed in the UI
3. Test the connection using the standalone test script
4. Verify the database server is accessible from your network
