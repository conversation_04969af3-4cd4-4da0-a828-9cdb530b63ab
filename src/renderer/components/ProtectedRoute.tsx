import React, { useEffect, useState } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { LoadingSpinner } from './LoadingSpinner';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRole?: string;
  fallbackPath?: string;
}

export function ProtectedRoute({ 
  children, 
  requiredRole, 
  fallbackPath = '/login' 
}: ProtectedRouteProps) {
  const { isAuthenticated, user, isLoading, validateSession } = useAuth();
  const [isValidating, setIsValidating] = useState(true);
  const location = useLocation();

  useEffect(() => {
    const checkAuth = async () => {
      console.log('ProtectedRoute: checkAuth called', { isLoading, isAuthenticated });

      if (isLoading) {
        console.log('ProtectedRoute: Still loading auth state');
        return; // Wait for initial auth state to load
      }

      if (!isAuthenticated) {
        console.log('ProtectedRoute: Not authenticated, stopping validation');
        setIsValidating(false);
        return;
      }
      console.log("isAuthenticated",isAuthenticated)
      console.log('ProtectedRoute: User is authenticated, validating session');
      // Validate current session
      try {
        const isValid = await validateSession();
        console.log('ProtectedRoute: Session validation result:', isValid);
        if (!isValid) {
          console.log('Session validation failed, auth state should be cleared');
          // The validateSession function in AuthContext should handle clearing auth state
          // We don't need to do anything else here as the auth state change will trigger a re-render
        }
      } catch (error) {
        console.error('Session validation error:', error);
      } finally {
        setIsValidating(false);
      }
    };

    checkAuth();
  }, [isAuthenticated, isLoading, validateSession]);

  // Show loading spinner while checking authentication
  if (isLoading || isValidating) {
    return <LoadingSpinner />;
  }

  // Redirect to login if not authenticated
  if (!isAuthenticated) {
    return (
      <Navigate 
        to={fallbackPath} 
        state={{ from: location }} 
        replace 
      />
    );
  }

  // Check role-based access if required
  if (requiredRole && user?.role_code !== requiredRole) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full bg-white shadow-lg rounded-lg p-6 text-center">
          <div className="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-red-100 mb-4">
            <svg className="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Access Denied</h3>
          <p className="text-sm text-gray-600 mb-4">
            You don't have permission to access this page. Required role: {requiredRole}
          </p>
          <p className="text-xs text-gray-500">
            Your current role: {user?.role_code || 'None'}
          </p>
        </div>
      </div>
    );
  }

  // Render protected content
  return <>{children}</>;
}

// Higher-order component for protecting routes
export function withAuth<P extends object>(
  Component: React.ComponentType<P>,
  requiredRole?: string
) {
  return function AuthenticatedComponent(props: P) {
    return (
      <ProtectedRoute requiredRole={requiredRole}>
        <Component {...props} />
      </ProtectedRoute>
    );
  };
}

// Hook for checking if current user can access a feature
export function useCanAccess(requiredRole?: string): boolean {
  const { isAuthenticated, user } = useAuth();
  
  if (!isAuthenticated) {
    return false;
  }
  
  if (!requiredRole) {
    return true;
  }
  
  return user?.role_code === requiredRole;
}

// Component for conditionally rendering content based on role
interface RoleGuardProps {
  children: React.ReactNode;
  requiredRole?: string;
  fallback?: React.ReactNode;
}

export function RoleGuard({ children, requiredRole, fallback = null }: RoleGuardProps) {
  const canAccess = useCanAccess(requiredRole);
  
  return canAccess ? <>{children}</> : <>{fallback}</>;
}
