import React, { createContext, useContext, useState, useEffect, useCallback, ReactNode } from 'react';
import { safeIpcInvoke } from '../utils/electron';

// Types
interface User {
  user_id: number;
  user_ref: string;
  user_name: string;
  role_code?: string;
  role_name?: string;
  last_login?: Date;
}

interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  sessionId: string | null;
  expiresAt: Date | null;
  isLoading: boolean;
}

interface LoginCredentials {
  username: string;
  password: string;
  force_login?: boolean;
}

interface LoginResult {
  success: boolean;
  message: string;
  error?: string;
}

interface AuthContextType extends AuthState {
  login: (credentials: LoginCredentials) => Promise<LoginResult>;
  logout: () => Promise<void>;
  validateSession: () => Promise<boolean>;
  clearAuth: () => void;
}

// Create context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Storage keys
const STORAGE_KEYS = {
  USER: 'auth_user',
  SESSION_ID: 'auth_session_id',
  EXPIRES_AT: 'auth_expires_at'
};

// Auth Provider Component
interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [authState, setAuthState] = useState<AuthState>({
    isAuthenticated: false,
    user: null,
    sessionId: null,
    expiresAt: null,
    isLoading: true
  });

  // Get client info for logging
  const getClientInfo = async () => {
    let ip_address = '127.0.0.1'; // Default to localhost
    try {
      const result = await safeIpcInvoke('get-ip-address');
      if (result && result.ip_address) {
        ip_address = result.ip_address;
      }
    } catch (error) {
      console.error('Failed to get IP address from main process:', error);
    }
    return {
      ip_address,
      user_agent: navigator.userAgent
    };
  };

  // Load auth state from localStorage on mount
  useEffect(() => {
    const loadAuthState = async () => {
      try {
        const storedUser = localStorage.getItem(STORAGE_KEYS.USER);
        const storedSessionId = localStorage.getItem(STORAGE_KEYS.SESSION_ID);
        const storedExpiresAt = localStorage.getItem(STORAGE_KEYS.EXPIRES_AT);

        if (storedUser && storedSessionId && storedExpiresAt) {
          const user = JSON.parse(storedUser);
          const expiresAt = new Date(storedExpiresAt);

          // Check if session is still valid
          if (new Date() < expiresAt) {
            // Validate session with server
            const isValid = await validateSessionWithServer(user.user_id, storedSessionId);
            
            if (isValid) {
              setAuthState({
                isAuthenticated: true,
                user,
                sessionId: storedSessionId,
                expiresAt,
                isLoading: false
              });
              
              // Set up auto-logout timer
              setupAutoLogout(expiresAt);
              return;
            }
          }
        }

        // Clear invalid/expired session
        clearAuthState();
      } catch (error) {
        console.error('Error loading auth state:', error);
        clearAuthState();
      }
    };

    loadAuthState();
  }, []);

  // Validate session with server
  const validateSessionWithServer = async (userId: number, sessionId: string): Promise<boolean> => {
    try {
      const result = await safeIpcInvoke('validate-session', { user_id: userId, session_id: sessionId });
      return result.valid;
    } catch (error) {
      console.error('Session validation error:', error);
      return false;
    }
  };

  // Setup auto-logout timer
  const setupAutoLogout = (expiresAt: Date) => {
    const timeUntilExpiry = expiresAt.getTime() - Date.now();
    
    if (timeUntilExpiry > 0) {
      setTimeout(() => {
        logout();
      }, timeUntilExpiry);
    }
  };

  // Clear auth state
  const clearAuthState = () => {
    localStorage.removeItem(STORAGE_KEYS.USER);
    localStorage.removeItem(STORAGE_KEYS.SESSION_ID);
    localStorage.removeItem(STORAGE_KEYS.EXPIRES_AT);
    
    setAuthState({
      isAuthenticated: false,
      user: null,
      sessionId: null,
      expiresAt: null,
      isLoading: false
    });
  };

  // Login function
  const login = async (credentials: LoginCredentials): Promise<LoginResult> => {
    try {
      setAuthState(prev => ({ ...prev, isLoading: true }));

      const clientInfo = await getClientInfo();
      console.log("clientInfo",clientInfo)
      const result = await safeIpcInvoke('user-login', {
        username: credentials.username,
        password: credentials.password,
        force_login: credentials.force_login,
        ...clientInfo
      });

      if (result.success) {
        const expiresAt = new Date(result.expires_at);
        
        // Store auth data
        localStorage.setItem(STORAGE_KEYS.USER, JSON.stringify(result.user));
        localStorage.setItem(STORAGE_KEYS.SESSION_ID, result.session_id);
        localStorage.setItem(STORAGE_KEYS.EXPIRES_AT, expiresAt.toISOString());

        // Update state
        console.log('AuthContext: Setting authenticated state', result.user);
        setAuthState({
          isAuthenticated: true,
          user: result.user,
          sessionId: result.session_id,
          expiresAt,
          isLoading: false
        });
        console.log('AuthContext: Authentication state updated');

        // Setup auto-logout
        setupAutoLogout(expiresAt);

        return { success: true, message: result.message };
      } else {
        setAuthState(prev => ({ ...prev, isLoading: false }));
        return { 
          success: false, 
          message: result.message,
          error: result.error 
        };
      }
    } catch (error: any) {
      setAuthState(prev => ({ ...prev, isLoading: false }));
      console.error('Login error:', error);
      return { success: false, message: 'Login failed due to connection error' };
    }
  };

  // Logout function
  const logout = async (): Promise<void> => {
    try {
      if (authState.user && authState.sessionId) {
        const clientInfo = getClientInfo();
        await safeIpcInvoke('user-logout', {
          user_id: authState.user.user_id,
          session_id: authState.sessionId,
          ...clientInfo
        });
      }
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      clearAuthState();
    }
  };

  // Validate current session
  const validateSession = useCallback(async (): Promise<boolean> => {
    if (!authState.user || !authState.sessionId) {
      return false;
    }

    try {
      const isValid = await validateSessionWithServer(authState.user.user_id, authState.sessionId);

      if (!isValid) {
        clearAuthState();
      }

      return isValid;
    } catch (error) {
      console.error('Session validation error:', error);
      clearAuthState();
      return false;
    }
  }, [authState.user, authState.sessionId]);

  // Clear auth (for manual logout)
  const clearAuth = () => {
    clearAuthState();
  };

  const contextValue: AuthContextType = {
    ...authState,
    login,
    logout,
    validateSession,
    clearAuth
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
}

// Hook to use auth context
export function useAuth(): AuthContextType {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

// Hook to check if user has specific role
export function useRole(requiredRole?: string): boolean {
  const { user } = useAuth();
  
  if (!requiredRole || !user) {
    return true;
  }
  
  return user.role_code === requiredRole;
}

// Hook to check if user is admin
export function useIsAdmin(): boolean {
  return useRole('admin');
}
