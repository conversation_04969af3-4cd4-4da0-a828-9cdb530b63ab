import React, { useState, useEffect } from "react";
import { Modal } from "../components/modal";
import { Button } from "../components/button";

interface NetworkServiceFee {
  id?: number;
  category: string;
  subcategory: string;
  card_type?: string;
  fee_type: 'percentage' | 'fixed';
  fee_value: number;
  min_amount?: number;
  max_amount?: number;
  currency: string;
  active: boolean;
  create_by: string;
  create_dt?: string;
  update_by?: string;
  update_dt?: string;
}

export function NetworkServiceScreen() {
  const [fees, setFees] = useState<NetworkServiceFee[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingFee, setEditingFee] = useState<NetworkServiceFee | null>(null);
  const [formData, setFormData] = useState<Omit<NetworkServiceFee, "id" | "create_dt" | "update_dt">>({
    category: "",
    subcategory: "",
    card_type: "",
    fee_type: "percentage",
    fee_value: 0,
    min_amount: 0,
    max_amount: 0,
    currency: "THB",
    active: true,
    create_by: "SYSTEM",
  });

  // Mock data based on the image
  const mockFees: NetworkServiceFee[] = [
    // International (Purchase, pre-authorization in non-E-commerce channel and corresponding refund transactions)
    { id: 1, category: "International", subcategory: "Consumer Card", card_type: "Dual Brand Indicator=0", fee_type: "percentage", fee_value: 1.200, currency: "THB", active: true, create_by: "SYSTEM" },
    { id: 2, category: "International", subcategory: "Consumer Card", card_type: "Prepaid Card and PIN-based Debit Card (D,P)", fee_type: "percentage", fee_value: 1.150, currency: "THB", active: true, create_by: "SYSTEM" },
    { id: 3, category: "International", subcategory: "Consumer Card", card_type: "Credit Card and Signature-based Debit Card (C) - Classic_Gold", fee_type: "percentage", fee_value: 1.200, currency: "THB", active: true, create_by: "SYSTEM" },
    { id: 4, category: "International", subcategory: "Consumer Card", card_type: "Credit Card and Signature-based Debit Card (C) - Platinum", fee_type: "percentage", fee_value: 1.950, currency: "THB", active: true, create_by: "SYSTEM" },
    { id: 5, category: "International", subcategory: "Consumer Card", card_type: "Credit Card and Signature-based Debit Card (C) - Diamond and above", fee_type: "percentage", fee_value: 2.100, currency: "THB", active: true, create_by: "SYSTEM" },
    { id: 6, category: "International", subcategory: "Commercial Card", card_type: "Amount Per Transactions > 2,000 US and Non-T/E MCCs", fee_type: "percentage", fee_value: 0.750, min_amount: 25.000, currency: "US", active: true, create_by: "SYSTEM" },
    { id: 7, category: "International", subcategory: "Commercial Card", card_type: "T_E Merchants", fee_type: "percentage", fee_value: 2.000, currency: "THB", active: true, create_by: "SYSTEM" },
    
    // THAI PAYMENT NETWORK FEE
    { id: 8, category: "THAI PAYMENT NETWORK FEE", subcategory: "POS", card_type: "Merchant Category : Government", fee_type: "fixed", fee_value: 0.000, currency: "Baht Per Transactions", active: true, create_by: "SYSTEM" },
    { id: 9, category: "THAI PAYMENT NETWORK FEE", subcategory: "POS", card_type: "Merchant Category : Non-Government", fee_type: "percentage", fee_value: 0.300, max_amount: 0.300, currency: "Baht", active: true, create_by: "SYSTEM" },
    { id: 10, category: "THAI PAYMENT NETWORK FEE", subcategory: "E-COMMERCE", card_type: "Merchant Category : Government", fee_type: "fixed", fee_value: 0.000, currency: "Baht Per Transactions", active: true, create_by: "SYSTEM" },
    { id: 11, category: "THAI PAYMENT NETWORK FEE", subcategory: "E-COMMERCE", card_type: "Merchant Category : Non-Government", fee_type: "percentage", fee_value: 0.600, max_amount: 0.600, currency: "Baht", active: true, create_by: "SYSTEM" },
  ];

  useEffect(() => {
    // Load mock data
    setFees(mockFees);
  }, []);

  const handleCreate = () => {
    setEditingFee(null);
    setFormData({
      category: "",
      subcategory: "",
      card_type: "",
      fee_type: "percentage",
      fee_value: 0,
      min_amount: 0,
      max_amount: 0,
      currency: "THB",
      active: true,
      create_by: "SYSTEM",
    });
    setIsModalOpen(true);
  };

  const handleEdit = (fee: NetworkServiceFee) => {
    setEditingFee(fee);
    setFormData({
      category: fee.category,
      subcategory: fee.subcategory,
      card_type: fee.card_type || "",
      fee_type: fee.fee_type,
      fee_value: fee.fee_value,
      min_amount: fee.min_amount || 0,
      max_amount: fee.max_amount || 0,
      currency: fee.currency,
      active: fee.active,
      create_by: fee.create_by,
      update_by: "SYSTEM",
    });
    setIsModalOpen(true);
  };

  const handleInputChange = (field: keyof typeof formData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Mock save operation
    if (editingFee && editingFee.id) {
      // Update existing fee
      setFees(prev => prev.map(fee => 
        fee.id === editingFee.id 
          ? { ...fee, ...formData, update_dt: new Date().toISOString() }
          : fee
      ));
      alert("Network service fee updated successfully");
    } else {
      // Create new fee
      const newFee: NetworkServiceFee = {
        ...formData,
        id: Math.max(...fees.map(f => f.id || 0)) + 1,
        create_dt: new Date().toISOString()
      };
      setFees(prev => [...prev, newFee]);
      alert("Network service fee created successfully");
    }
    
    setIsModalOpen(false);
  };

  const handleDelete = async (fee: NetworkServiceFee) => {
    if (confirm(`Are you sure you want to delete this network service fee?`)) {
      setFees(prev => prev.filter(f => f.id !== fee.id));
      alert("Network service fee deleted successfully");
    }
  };

  const groupedFees = fees.reduce((acc, fee) => {
    const key = fee.category;
    if (!acc[key]) {
      acc[key] = [];
    }
    acc[key].push(fee);
    return acc;
  }, {} as Record<string, NetworkServiceFee[]>);

  return (
    <div className="min-h-screen bg-gray-50 p-4 sm:p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header Section */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Network Service Fee</h1>
              <p className="text-gray-600 mt-1">
                Manage network service fees and transaction charges
              </p>
            </div>
            <Button
              onClick={handleCreate}
              variant="primary"
              size="md"
              className="inline-flex items-center gap-2"
            >
              <svg
                className="w-5 h-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 4v16m8-8H4"
                />
              </svg>
              Add New Fee
            </Button>
          </div>
        </div>

        {/* Fee Categories */}
        <div className="space-y-6">
          {Object.entries(groupedFees).map(([category, categoryFees]) => (
            <div key={category} className="bg-white rounded-xl shadow-sm border border-gray-200">
              <div className="p-6 border-b border-gray-200">
                <h2 className="text-xl font-semibold text-gray-900">{category}</h2>
              </div>
              
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Subcategory
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Card Type
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Fee
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Currency
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {categoryFees.map((fee) => (
                      <tr key={fee.id} className="hover:bg-gray-50 transition-colors">
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          {fee.subcategory}
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-900">
                          {fee.card_type || "-"}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {fee.fee_type === 'percentage' ? `${fee.fee_value}%` : fee.fee_value}
                          {fee.min_amount && fee.min_amount > 0 && (
                            <span className="text-xs text-gray-500 block">
                              Min: {fee.min_amount}
                            </span>
                          )}
                          {fee.max_amount && fee.max_amount > 0 && (
                            <span className="text-xs text-gray-500 block">
                              Max: {fee.max_amount}
                            </span>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {fee.currency}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            fee.active 
                              ? 'bg-green-100 text-green-800' 
                              : 'bg-red-100 text-red-800'
                          }`}>
                            {fee.active ? 'Active' : 'Inactive'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                          <button
                            onClick={() => handleEdit(fee)}
                            className="text-blue-600 hover:text-blue-900 transition-colors"
                          >
                            Edit
                          </button>
                          <button
                            onClick={() => handleDelete(fee)}
                            className="text-red-600 hover:text-red-900 transition-colors"
                          >
                            Delete
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Modal for Create/Edit */}
      <Modal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        size="lg"
      >
        <div className="p-6">
          <h3 className="text-2xl font-bold text-gray-900 mb-6">
            {editingFee ? "Edit Network Service Fee" : "Add New Network Service Fee"}
          </h3>

          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  Category <span className="text-red-500">*</span>
                </label>
                <select
                  value={formData.category}
                  onChange={(e) => handleInputChange("category", e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  required
                >
                  <option value="">Select Category</option>
                  <option value="International">International</option>
                  <option value="THAI PAYMENT NETWORK FEE">THAI PAYMENT NETWORK FEE</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  Subcategory <span className="text-red-500">*</span>
                </label>
                <select
                  value={formData.subcategory}
                  onChange={(e) => handleInputChange("subcategory", e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  required
                >
                  <option value="">Select Subcategory</option>
                  <option value="Consumer Card">Consumer Card</option>
                  <option value="Commercial Card">Commercial Card</option>
                  <option value="POS">POS</option>
                  <option value="E-COMMERCE">E-COMMERCE</option>
                </select>
              </div>
            </div>

            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-2">
                Card Type
              </label>
              <input
                type="text"
                value={formData.card_type}
                onChange={(e) => handleInputChange("card_type", e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="e.g., Dual Brand Indicator=0, Classic_Gold, etc."
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  Fee Type <span className="text-red-500">*</span>
                </label>
                <select
                  value={formData.fee_type}
                  onChange={(e) => handleInputChange("fee_type", e.target.value as 'percentage' | 'fixed')}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  required
                >
                  <option value="percentage">Percentage</option>
                  <option value="fixed">Fixed Amount</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  Fee Value <span className="text-red-500">*</span>
                </label>
                <input
                  type="number"
                  step="0.001"
                  value={formData.fee_value}
                  onChange={(e) => handleInputChange("fee_value", parseFloat(e.target.value) || 0)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="0.000"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  Currency <span className="text-red-500">*</span>
                </label>
                <select
                  value={formData.currency}
                  onChange={(e) => handleInputChange("currency", e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  required
                >
                  <option value="THB">THB</option>
                  <option value="USD">USD</option>
                  <option value="Baht">Baht</option>
                  <option value="Baht Per Transactions">Baht Per Transactions</option>
                </select>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  Minimum Amount
                </label>
                <input
                  type="number"
                  step="0.001"
                  value={formData.min_amount}
                  onChange={(e) => handleInputChange("min_amount", parseFloat(e.target.value) || 0)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="0.000"
                />
              </div>

              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  Maximum Amount
                </label>
                <input
                  type="number"
                  step="0.001"
                  value={formData.max_amount}
                  onChange={(e) => handleInputChange("max_amount", parseFloat(e.target.value) || 0)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="0.000"
                />
              </div>
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                id="active"
                checked={formData.active}
                onChange={(e) => handleInputChange("active", e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="active" className="ml-2 block text-sm text-gray-700">
                Active
              </label>
            </div>

            <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200">
              <Button
                type="button"
                variant="secondary"
                onClick={() => setIsModalOpen(false)}
              >
                Cancel
              </Button>
              <Button type="submit" variant="primary">
                {editingFee ? "Update Fee" : "Create Fee"}
              </Button>
            </div>
          </form>
        </div>
      </Modal>
    </div>
  );
}
