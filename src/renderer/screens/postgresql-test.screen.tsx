import React, { useState } from 'react';
import { safeIpcInvoke } from '../utils/electron';

interface PostgreSQLConnectionConfig {
  host: string;
  port: number;
  database: string;
  user: string;
  password: string;
  ssl?: boolean;
}

interface PostgreSQLConnectionResult {
  success: boolean;
  message: string;
  connectionTime?: number;
  serverVersion?: string;
  error?: string;
}

export function PostgreSQLTestScreen() {
  const [config, setConfig] = useState<PostgreSQLConnectionConfig>({
    host: 'ep-dawn-fog-a1jk7z7f-pooler.ap-southeast-1.aws.neon.tech',
    port: 5432,
    database: 'neondb',
    user: 'neondb_owner',
    password: 'npg_v1aKnJdNXif4',
    ssl: true,
  });

  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<PostgreSQLConnectionResult | null>(null);
  const [showPassword, setShowPassword] = useState(false);

  const handleInputChange = (field: keyof PostgreSQLConnectionConfig, value: string | number | boolean) => {
    setConfig(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const testConnection = async () => {
    setIsLoading(true);
    setResult(null);

    try {
      const connectionResult = await safeIpcInvoke('test-postgresql-connection', config);
      setResult(connectionResult);
    } catch (error: any) {
      setResult({
        success: false,
        message: error.message || 'Failed to test connection',
        error: 'IPC_ERROR'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const resetForm = () => {
    setConfig({
      host: '',
      port: 5432,
      database: '',
      user: '',
      password: '',
      ssl: false,
    });
    setResult(null);
  };

  const loadNeonExample = () => {
    setConfig({
      host: 'ep-dawn-fog-a1jk7z7f-pooler.ap-southeast-1.aws.neon.tech',
      port: 5432,
      database: 'neondb',
      user: 'neondb_owner',
      password: 'npg_v1aKnJdNXif4',
      ssl: true,
    });
    setResult(null);
  };

  return (
    <div className="w-full max-w-4xl mx-auto p-6">
      <div className="bg-white rounded-lg shadow-lg p-6">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-2xl font-bold text-gray-800">PostgreSQL Connection Test</h1>
          <div className="flex gap-2">
            <button
              onClick={loadNeonExample}
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
            >
              Load Neon Example
            </button>
            <button
              onClick={resetForm}
              className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
            >
              Reset Form
            </button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Connection Form */}
          <div className="space-y-4">
            <h2 className="text-lg font-semibold text-gray-700 mb-4">Connection Details</h2>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Host</label>
              <input
                type="text"
                value={config.host}
                onChange={(e) => handleInputChange('host', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="localhost or hostname"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Port</label>
              <input
                type="number"
                value={config.port}
                onChange={(e) => handleInputChange('port', parseInt(e.target.value) || 5432)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="5432"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Database</label>
              <input
                type="text"
                value={config.database}
                onChange={(e) => handleInputChange('database', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="database name"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Username</label>
              <input
                type="text"
                value={config.user}
                onChange={(e) => handleInputChange('user', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="username"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Password</label>
              <div className="relative">
                <input
                  type={showPassword ? 'text' : 'password'}
                  value={config.password}
                  onChange={(e) => handleInputChange('password', e.target.value)}
                  className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="password"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center text-sm leading-5"
                >
                  {showPassword ? '🙈' : '👁️'}
                </button>
              </div>
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                id="ssl"
                checked={config.ssl || false}
                onChange={(e) => handleInputChange('ssl', e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="ssl" className="ml-2 block text-sm text-gray-700">
                Use SSL Connection
              </label>
            </div>

            <button
              onClick={testConnection}
              disabled={isLoading || !config.host || !config.database || !config.user}
              className="w-full px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
            >
              {isLoading ? 'Testing Connection...' : 'Test Connection'}
            </button>
          </div>

          {/* Results Panel */}
          <div className="space-y-4">
            <h2 className="text-lg font-semibold text-gray-700 mb-4">Connection Result</h2>
            
            {isLoading && (
              <div className="flex items-center justify-center p-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <span className="ml-2 text-gray-600">Testing connection...</span>
              </div>
            )}

            {result && (
              <div className={`p-4 rounded-md ${result.success ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'}`}>
                <div className="flex items-center mb-2">
                  <span className={`text-lg ${result.success ? 'text-green-600' : 'text-red-600'}`}>
                    {result.success ? '✅' : '❌'}
                  </span>
                  <span className={`ml-2 font-medium ${result.success ? 'text-green-800' : 'text-red-800'}`}>
                    {result.success ? 'Connection Successful' : 'Connection Failed'}
                  </span>
                </div>
                
                <p className={`text-sm ${result.success ? 'text-green-700' : 'text-red-700'} mb-3`}>
                  {result.message}
                </p>

                <div className="space-y-2 text-sm">
                  {result.connectionTime && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Connection Time:</span>
                      <span className="font-mono">{result.connectionTime}ms</span>
                    </div>
                  )}
                  
                  {result.serverVersion && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Server Version:</span>
                      <span className="font-mono">{result.serverVersion}</span>
                    </div>
                  )}
                  
                  {result.error && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Error Code:</span>
                      <span className="font-mono text-red-600">{result.error}</span>
                    </div>
                  )}
                </div>
              </div>
            )}

            {!result && !isLoading && (
              <div className="p-4 bg-gray-50 border border-gray-200 rounded-md">
                <p className="text-gray-600 text-center">
                  Fill in the connection details and click "Test Connection" to verify your PostgreSQL database connection.
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Connection String Display */}
        <div className="mt-6 p-4 bg-gray-50 border border-gray-200 rounded-md">
          <h3 className="text-sm font-medium text-gray-700 mb-2">Connection String (for reference):</h3>
          <code className="text-xs text-gray-600 break-all">
            postgresql://{config.user}:{config.password ? '***' : ''}@{config.host}:{config.port}/{config.database}{config.ssl ? '?sslmode=require' : ''}
          </code>
        </div>
      </div>
    </div>
  );
}
