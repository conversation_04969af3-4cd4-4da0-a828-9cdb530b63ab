import React, { useState, useEffect, useCallback } from "react";
import { safeIpcInvoke } from "../utils/electron";
import { Modal } from "../components/modal";
import { Button } from "../components/button";

interface BankMaster {
  bank_id?: number;
  bank_code: string;
  bank_name_th?: string;
  bank_name_en?: string;
  bank_address_th?: string;
  bank_address_en?: string;
  active: boolean;
  create_by: string;
  create_dt?: string;
  update_by?: string;
  update_dt?: string;
}

interface BankMasterResponse {
  success: boolean;
  message: string;
  data?: BankMaster | BankMaster[];
  pagination?: {
    currentPage: number;
    totalPages: number;
    totalRecords: number;
    pageSize: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
  error?: string;
}

interface PaginationParams {
  page?: number;
  pageSize?: number;
  search?: string;
  sortBy?: string;
  sortOrder?: "ASC" | "DESC";
}

export function BankMasterScreen() {
  const [banks, setBanks] = useState<BankMaster[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingBank, setEditingBank] = useState<BankMaster | null>(null);
  const [formData, setFormData] = useState<
    Omit<BankMaster, "bank_id" | "create_dt" | "update_dt">
  >({
    bank_code: "",
    bank_name_th: "",
    bank_name_en: "",
    bank_address_th: "",
    bank_address_en: "",
    active: true,
    create_by: "SYSTEM",
  });

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(() => {
    const saved = localStorage.getItem('bankMaster_pageSize');
    return saved ? parseInt(saved) : 10;
  });
  const [totalPages, setTotalPages] = useState(0);
  const [totalRecords, setTotalRecords] = useState(0);
  const [hasNextPage, setHasNextPage] = useState(false);
  const [hasPreviousPage, setHasPreviousPage] = useState(false);

  // Search and sort state
  const [searchTerm, setSearchTerm] = useState("");
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");
  const [sortBy, setSortBy] = useState("bank_id");
  const [sortOrder, setSortOrder] = useState<"ASC" | "DESC">("ASC");

  // Load banks on component mount
  useEffect(() => {
    loadBanks();
  }, []);

  // Debounce search term
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 500); // 500ms delay

    return () => clearTimeout(timer);
  }, [searchTerm]);

  // Load banks when debounced search term changes
  useEffect(() => {
    if (debouncedSearchTerm !== searchTerm) return; // Only load if search has settled
    setCurrentPage(1);
    loadBanks(1, debouncedSearchTerm);
  }, [debouncedSearchTerm]);

  // Reload when sort changes
  useEffect(() => {
    if (sortBy && sortOrder) {
      loadBanks(currentPage, debouncedSearchTerm);
    }
  }, [sortBy, sortOrder]);

  const loadBanks = async (
    page: number = currentPage,
    search: string = debouncedSearchTerm,
    pageSize: number = 10
  ) => {
    setIsLoading(true);
    try {
      const params: PaginationParams = {
        page,
        pageSize,
        search: search.trim(),
        sortBy,
        sortOrder,
      };

      const response: BankMasterResponse = await safeIpcInvoke(
        "get-banks",
        params
      );
      if (response.success && Array.isArray(response.data)) {
        setBanks(response.data);

        // Update pagination state
        if (response.pagination) {
          setCurrentPage(response.pagination.currentPage);
          setTotalPages(response.pagination.totalPages);
          setTotalRecords(response.pagination.totalRecords);
          setHasNextPage(response.pagination.hasNextPage);
          setHasPreviousPage(response.pagination.hasPreviousPage);
        }
      } else {
        console.error("Failed to load banks:", response.message);
        setBanks([]);
      }
    } catch (error) {
      console.error("Error loading banks:", error);
      setBanks([]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreate = () => {
    setEditingBank(null);
    setFormData({
      bank_code: "",
      bank_name_th: "",
      bank_name_en: "",
      bank_address_th: "",
      bank_address_en: "",
      active: true,
      create_by: "SYSTEM",
    });
    setIsModalOpen(true);
  };

  const handleEdit = (bank: BankMaster) => {
    setEditingBank(bank);
    setFormData({
      bank_code: bank.bank_code,
      bank_name_th: bank.bank_name_th || "",
      bank_name_en: bank.bank_name_en || "",
      bank_address_th: bank.bank_address_th || "",
      bank_address_en: bank.bank_address_en || "",
      active: bank.active,
      create_by: bank.create_by,
      update_by: "SYSTEM",
    });
    setIsModalOpen(true);
  };

  const handleDelete = async (bank: BankMaster) => {
    const bankName = bank.bank_name_th || bank.bank_name_en || "this bank";
    if (!confirm(`Are you sure you want to delete ${bankName}?`)) {
      return;
    }

    if (!bank.bank_id) {
      alert("Cannot delete bank: Invalid bank ID");
      return;
    }

    try {
      const response: BankMasterResponse = await safeIpcInvoke(
        "delete-bank",
        bank.bank_id
      );
      if (response.success) {
        await loadBanks(); // Reload the list
        alert("Bank deleted successfully");
      } else {
        alert(`Failed to delete bank: ${response.message}`);
      }
    } catch (error) {
      console.error("Error deleting bank:", error);
      alert("Error deleting bank");
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.bank_code.trim()) {
      alert("Bank code is required");
      return;
    }

    if (!formData.bank_name_th?.trim() && !formData.bank_name_en?.trim()) {
      alert("At least one bank name (Thai or English) is required");
      return;
    }

    if (formData.bank_code.length > 20) {
      alert("Bank code must be 20 characters or less");
      return;
    }

    try {
      let response: BankMasterResponse;

      if (editingBank && editingBank.bank_id) {
        // Update existing bank
        response = await safeIpcInvoke(
          "update-bank",
          editingBank.bank_id,
          formData
        );
      } else {
        // Create new bank
        response = await safeIpcInvoke("create-bank", formData);
      }

      if (response.success) {
        setIsModalOpen(false);
        await loadBanks(); // Reload the list
        alert(
          editingBank
            ? "Bank updated successfully"
            : "Bank created successfully"
        );
      } else {
        alert(
          `Failed to ${editingBank ? "update" : "create"} bank: ${
            response.message
          }`
        );
      }
    } catch (error) {
      console.error("Error saving bank:", error);
      alert("Error saving bank");
    }
  };

  const handleInputChange = (
    field: keyof typeof formData,
    value: string | boolean
  ) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  // Pagination functions
  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= totalPages) {
      setCurrentPage(newPage);
      loadBanks(newPage, searchTerm, pageSize);
    }
  };

  const handlePageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize);
    localStorage.setItem('bankMaster_pageSize', newPageSize.toString());
    setCurrentPage(1);
    loadBanks(1, searchTerm, newPageSize);
  };

  const handleSearch = (search: string) => {
    setSearchTerm(search);
    // The actual search will be triggered by the debounced effect
  };

  const handleSort = (column: string) => {
    const newSortOrder =
      sortBy === column && sortOrder === "ASC" ? "DESC" : "ASC";
    setSortBy(column);
    setSortOrder(newSortOrder);
    loadBanks(currentPage, searchTerm);
  };

  const formatDate = (dateStr?: string) => {
    if (!dateStr) return "-";
    return new Date(dateStr).toLocaleDateString();
  };

  console.log("formData", formData);

  return (
    <div className="min-h-screen bg-gray-50 p-4 sm:p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header Section */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Bank Master</h1>
              <p className="text-gray-600 mt-1">
                Manage your bank information and settings
              </p>
            </div>
            <Button
              onClick={handleCreate}
              variant="primary"
              size="md"
              className="inline-flex items-center gap-2"
            >
              <svg
                className="w-5 h-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 4v16m8-8H4"
                />
              </svg>
              Add New Bank
            </Button>
          </div>
        </div>

        {/* Search and Controls Section */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
          <div className="flex flex-col lg:flex-row gap-4">
            {/* Search Input */}
            <div className="flex-1">
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg
                    className="h-5 w-5 text-gray-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                    />
                  </svg>
                </div>
                <input
                  type="text"
                  placeholder="Search by bank code or name..."
                  value={searchTerm}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>

            {/* Page Size Selector */}
            <div className="flex items-center gap-3 bg-gray-50 rounded-lg px-4 py-2">
              <label className="text-sm font-medium text-gray-700 whitespace-nowrap">
                Show:
              </label>
              <select
                value={pageSize}
                onChange={(e) => handlePageSizeChange(parseInt(e.target.value))}
                className="border-0 bg-transparent focus:outline-none focus:ring-0 text-sm font-medium text-gray-700"
              >
                <option value={5}>5</option>
                <option value={10}>10</option>
                <option value={20}>20</option>
                <option value={50}>50</option>
              </select>
              <span className="text-sm text-gray-600 whitespace-nowrap">
                entries
              </span>
            </div>
          </div>
        </div>

        {/* Loading State */}
        {isLoading && (
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-12">
            <div className="flex flex-col items-center justify-center">
              <div className="animate-spin rounded-full h-12 w-12 border-4 border-gray-200 border-t-blue-500"></div>
              <span className="mt-4 text-gray-600 font-medium">
                Loading banks...
              </span>
            </div>
          </div>
        )}

        {/* Banks Table */}
        {!isLoading && (
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th
                      className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 transition-colors"
                      onClick={() => handleSort("bank_id")}
                    >
                      <div className="flex items-center gap-1">
                        ID
                        {sortBy === "bank_id" && (
                          <span className="text-blue-500">
                            {sortOrder === "ASC" ? "↑" : "↓"}
                          </span>
                        )}
                      </div>
                    </th>
                    <th
                      className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 transition-colors"
                      onClick={() => handleSort("bank_code")}
                    >
                      <div className="flex items-center gap-1">
                        Bank Code
                        {sortBy === "bank_code" && (
                          <span className="text-blue-500">
                            {sortOrder === "ASC" ? "↑" : "↓"}
                          </span>
                        )}
                      </div>
                    </th>
                    <th
                      className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 transition-colors"
                      onClick={() => handleSort("bank_name_th")}
                    >
                      <div className="flex items-center gap-1">
                        Bank Name (TH)
                        {sortBy === "bank_name_th" && (
                          <span className="text-blue-500">
                            {sortOrder === "ASC" ? "↑" : "↓"}
                          </span>
                        )}
                      </div>
                    </th>
                    <th
                      className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 transition-colors"
                      onClick={() => handleSort("bank_name_en")}
                    >
                      <div className="flex items-center gap-1">
                        Bank Name (EN)
                        {sortBy === "bank_name_en" && (
                          <span className="text-blue-500">
                            {sortOrder === "ASC" ? "↑" : "↓"}
                          </span>
                        )}
                      </div>
                    </th>
                    <th
                      className="px-6 py-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 transition-colors"
                      onClick={() => handleSort("active")}
                    >
                      <div className="flex items-center justify-center gap-1">
                        Status
                        {sortBy === "active" && (
                          <span className="text-blue-500">
                            {sortOrder === "ASC" ? "↑" : "↓"}
                          </span>
                        )}
                      </div>
                    </th>
                    <th
                      className="px-6 py-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 transition-colors"
                      onClick={() => handleSort("create_dt")}
                    >
                      <div className="flex items-center justify-center gap-1">
                        Created
                        {sortBy === "create_dt" && (
                          <span className="text-blue-500">
                            {sortOrder === "ASC" ? "↑" : "↓"}
                          </span>
                        )}
                      </div>
                    </th>
                    <th
                      className="px-6 py-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 transition-colors"
                      onClick={() => handleSort("update_dt")}
                    >
                      <div className="flex items-center justify-center gap-1">
                        Updated
                        {sortBy === "update_dt" && (
                          <span className="text-blue-500">
                            {sortOrder === "ASC" ? "↑" : "↓"}
                          </span>
                        )}
                      </div>
                    </th>
                    <th className="px-6 py-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {banks.length === 0 ? (
                    <tr>
                      <td colSpan={7} className="px-6 py-12 text-center">
                        <div className="flex flex-col items-center">
                          <svg
                            className="w-12 h-12 text-gray-400 mb-4"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
                            />
                          </svg>
                          <h3 className="text-lg font-medium text-gray-900 mb-2">
                            No banks found
                          </h3>
                          <p className="text-gray-500 mb-4">
                            Get started by creating your first bank
                          </p>
                          <Button
                            onClick={handleCreate}
                            variant="primary"
                            size="sm"
                          >
                            Add New Bank
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ) : (
                    banks.map((bank) => (
                      <tr
                        key={bank.bank_id || bank.bank_code}
                        className="hover:bg-gray-50 transition-colors"
                      >
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          #{bank.bank_id}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            {bank.bank_code}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-medium">
                          {bank.bank_name_th || "-"}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-medium">
                          {bank.bank_name_en || "-"}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-center">
                          <span
                            className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              bank.active
                                ? "bg-green-100 text-green-800"
                                : "bg-red-100 text-red-800"
                            }`}
                          >
                            {bank.active ? "Active" : "Inactive"}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-center text-sm text-gray-500">
                          <div>
                            <div className="font-medium">
                              {formatDate(bank.create_dt)}
                            </div>
                            <div className="text-xs text-gray-400">
                              by {bank.create_by}
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-center text-sm text-gray-500">
                          <div>
                            <div className="font-medium">
                              {formatDate(bank.update_dt)}
                            </div>
                            {bank.update_by && (
                              <div className="text-xs text-gray-400">
                                by {bank.update_by}
                              </div>
                            )}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-center text-sm font-medium">
                          <div className="flex items-center justify-center gap-2">
                            <Button
                              onClick={() => handleEdit(bank)}
                              variant="secondary"
                              size="sm"
                              className="inline-flex items-center gap-1"
                            >
                              <svg
                                className="w-4 h-4"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                                />
                              </svg>
                              Edit
                            </Button>
                            <Button
                              onClick={() => handleDelete(bank)}
                              variant="danger"
                              size="sm"
                              className="inline-flex items-center gap-1"
                            >
                              <svg
                                className="w-4 h-4"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                                />
                              </svg>
                              Delete
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {/* Pagination Controls */}
        {!isLoading && totalPages > 1 && (
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mt-6">
            <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
              <div className="text-sm text-gray-600">
                Showing{" "}
                <span className="font-medium">
                  {banks.length > 0 ? (currentPage - 1) * pageSize + 1 : 0}
                </span>{" "}
                to{" "}
                <span className="font-medium">
                  {Math.min(currentPage * pageSize, totalRecords)}
                </span>{" "}
                of <span className="font-medium">{totalRecords}</span> banks
              </div>

              <div className="flex items-center gap-1">
                <Button
                  onClick={() => handlePageChange(1)}
                  disabled={!hasPreviousPage}
                  variant="secondary"
                  size="sm"
                  className="px-3 py-1"
                >
                  <svg
                    className="w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M11 19l-7-7 7-7m8 14l-7-7 7-7"
                    />
                  </svg>
                </Button>

                <Button
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={!hasPreviousPage}
                  variant="secondary"
                  size="sm"
                  className="px-3 py-1"
                >
                  <svg
                    className="w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M15 19l-7-7 7-7"
                    />
                  </svg>
                </Button>

                <div className="flex items-center gap-1">
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    let pageNum;
                    if (totalPages <= 5) {
                      pageNum = i + 1;
                    } else if (currentPage <= 3) {
                      pageNum = i + 1;
                    } else if (currentPage >= totalPages - 2) {
                      pageNum = totalPages - 4 + i;
                    } else {
                      pageNum = currentPage - 2 + i;
                    }

                    return (
                      <Button
                        key={pageNum}
                        onClick={() => handlePageChange(pageNum)}
                        variant={
                          pageNum === currentPage ? "primary" : "secondary"
                        }
                        size="sm"
                        className="px-3 py-1 min-w-[36px]"
                      >
                        {pageNum}
                      </Button>
                    );
                  })}
                </div>

                <Button
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={!hasNextPage}
                  variant="secondary"
                  size="sm"
                  className="px-3 py-1"
                >
                  <svg
                    className="w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 5l7 7-7 7"
                    />
                  </svg>
                </Button>

                <Button
                  onClick={() => handlePageChange(totalPages)}
                  disabled={!hasNextPage}
                  variant="secondary"
                  size="sm"
                  className="px-3 py-1"
                >
                  <svg
                    className="w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M13 5l7 7-7 7M5 5l7 7-7 7"
                    />
                  </svg>
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Create/Edit Modal */}
      <Modal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        size="lg"
      >
        <div className="max-w-[500px]">
          <div className="mb-6">
            <h2 className="text-2xl font-bold text-gray-900">
              {editingBank ? "Edit Bank" : "Create New Bank"}
            </h2>
            <p className="text-gray-600 mt-1">
              {editingBank
                ? "Update bank information"
                : "Add a new bank to the system"}
            </p>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-2">
                Bank Code <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                value={formData.bank_code}
                onChange={(e) =>
                  handleInputChange("bank_code", e.target.value.toUpperCase())
                }
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="e.g., BBL, SCB, KTB"
                maxLength={20}
                required
              />
              <p className="text-xs text-gray-500 mt-1">
                Maximum 20 characters
              </p>
            </div>

            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-2">
                Bank Name (Thai)
              </label>
              <input
                type="text"
                value={formData.bank_name_th || ""}
                onChange={(e) =>
                  handleInputChange("bank_name_th", e.target.value)
                }
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="e.g., ธนาคารกรุงเทพ"
                maxLength={255}
              />
            </div>

            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-2">
                Bank Name (English)
              </label>
              <input
                type="text"
                value={formData.bank_name_en || ""}
                onChange={(e) =>
                  handleInputChange("bank_name_en", e.target.value)
                }
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="e.g., Bangkok Bank"
                maxLength={255}
              />
            </div>

            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-2">
                Bank Address (Thai)
              </label>
              <textarea
                value={formData.bank_address_th || ""}
                onChange={(e) =>
                  handleInputChange("bank_address_th", e.target.value)
                }
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="ที่อยู่ธนาคาร"
                rows={3}
              />
            </div>

            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-2">
                Bank Address (English)
              </label>
              <textarea
                value={formData.bank_address_en || ""}
                onChange={(e) =>
                  handleInputChange("bank_address_en", e.target.value)
                }
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Bank Address"
                rows={3}
              />
            </div>

            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-2">
                Status
              </label>
              <select
                value={formData.active.toString()}
                onChange={(e) =>
                  handleInputChange("active", e.target.value === "true")
                }
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="true">Active</option>
                <option value="false">Inactive</option>
              </select>
            </div>

            <div className="flex gap-3 pt-6">
              <Button
                type="submit"
                variant="primary"
                size="md"
                className="flex-1"
              >
                {editingBank ? "Update Bank" : "Create Bank"}
              </Button>
              <Button
                type="button"
                onClick={() => setIsModalOpen(false)}
                variant="secondary"
                size="md"
                className="flex-1"
              >
                Cancel
              </Button>
            </div>
          </form>
        </div>
      </Modal>
    </div>
  );
}
