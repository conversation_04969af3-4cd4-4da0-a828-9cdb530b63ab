import React, { useState } from 'react';
import { safeIpcInvoke } from '../utils/electron';

interface PCloudConnectionConfig {
  username: string;
  password: string;
  region?: 'us' | 'eu';
}

interface PCloudConnectionResult {
  success: boolean;
  message: string;
  connectionTime?: number;
  userInfo?: {
    email: string;
    emailVerified: boolean;
    premium: boolean;
    quota: number;
    usedQuota: number;
    language: string;
    registered: string;
  };
  error?: string;
}

export function PCloudTestScreen() {
  const [config, setConfig] = useState<PCloudConnectionConfig>({
    username: '<EMAIL>',
    password: '36890764',
    region: 'us',
  });

  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<PCloudConnectionResult | null>(null);
  const [showPassword, setShowPassword] = useState(false);

  const handleInputChange = (field: keyof PCloudConnectionConfig, value: string) => {
    setConfig(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const testConnection = async () => {
    setIsLoading(true);
    setResult(null);

    try {
      const connectionResult = await safeIpcInvoke('test-pcloud-connection', config);
      setResult(connectionResult);
    } catch (error: any) {
      setResult({
        success: false,
        message: error.message || 'Failed to test connection',
        error: 'IPC_ERROR'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const resetForm = () => {
    setConfig({
      username: '',
      password: '',
      region: 'us',
    });
    setResult(null);
  };

  const loadExampleCredentials = () => {
    setConfig({
      username: '<EMAIL>',
      password: '36890764',
      region: 'us',
    });
    setResult(null);
  };

  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="w-full max-w-4xl mx-auto p-6">
      <div className="bg-white rounded-lg shadow-lg p-6">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-2xl font-bold text-gray-800">pCloud Connection Test</h1>
          <div className="flex gap-2">
            <button
              onClick={loadExampleCredentials}
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
            >
              Load Example
            </button>
            <button
              onClick={resetForm}
              className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
            >
              Reset Form
            </button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Connection Form */}
          <div className="space-y-4">
            <h2 className="text-lg font-semibold text-gray-700 mb-4">Connection Details</h2>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Email/Username</label>
              <input
                type="email"
                value={config.username}
                onChange={(e) => handleInputChange('username', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="<EMAIL>"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Password</label>
              <div className="relative">
                <input
                  type={showPassword ? 'text' : 'password'}
                  value={config.password}
                  onChange={(e) => handleInputChange('password', e.target.value)}
                  className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="your password"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center text-sm leading-5"
                >
                  {showPassword ? '🙈' : '👁️'}
                </button>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Data Center Region</label>
              <select
                value={config.region}
                onChange={(e) => handleInputChange('region', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="us">United States (api.pcloud.com)</option>
                <option value="eu">Europe (eapi.pcloud.com)</option>
              </select>
              <p className="text-xs text-gray-500 mt-1">
                Choose the region where your pCloud account was created
              </p>
            </div>

            <button
              onClick={testConnection}
              disabled={isLoading || !config.username || !config.password}
              className="w-full px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
            >
              {isLoading ? 'Testing Connection...' : 'Test Connection'}
            </button>
          </div>

          {/* Results Panel */}
          <div className="space-y-4">
            <h2 className="text-lg font-semibold text-gray-700 mb-4">Connection Result</h2>
            
            {isLoading && (
              <div className="flex items-center justify-center p-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <span className="ml-2 text-gray-600">Testing connection...</span>
              </div>
            )}

            {result && (
              <div className={`p-4 rounded-md ${result.success ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'}`}>
                <div className="flex items-center mb-2">
                  <span className={`text-lg ${result.success ? 'text-green-600' : 'text-red-600'}`}>
                    {result.success ? '✅' : '❌'}
                  </span>
                  <span className={`ml-2 font-medium ${result.success ? 'text-green-800' : 'text-red-800'}`}>
                    {result.success ? 'Connection Successful' : 'Connection Failed'}
                  </span>
                </div>
                
                <p className={`text-sm ${result.success ? 'text-green-700' : 'text-red-700'} mb-3`}>
                  {result.message}
                </p>

                <div className="space-y-2 text-sm">
                  {result.connectionTime && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Connection Time:</span>
                      <span className="font-mono">{result.connectionTime}ms</span>
                    </div>
                  )}
                  
                  {result.userInfo && (
                    <>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Email:</span>
                        <span className="font-mono">{result.userInfo.email}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Email Verified:</span>
                        <span className={`font-mono ${result.userInfo.emailVerified ? 'text-green-600' : 'text-red-600'}`}>
                          {result.userInfo.emailVerified ? 'Yes' : 'No'}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Premium Account:</span>
                        <span className={`font-mono ${result.userInfo.premium ? 'text-blue-600' : 'text-gray-600'}`}>
                          {result.userInfo.premium ? 'Yes' : 'No'}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Storage Quota:</span>
                        <span className="font-mono">{formatBytes(result.userInfo.quota)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Used Storage:</span>
                        <span className="font-mono">{formatBytes(result.userInfo.usedQuota)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Available:</span>
                        <span className="font-mono text-green-600">
                          {formatBytes(result.userInfo.quota - result.userInfo.usedQuota)}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Language:</span>
                        <span className="font-mono">{result.userInfo.language.toUpperCase()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Registered:</span>
                        <span className="font-mono text-xs">{new Date(result.userInfo.registered).toLocaleDateString()}</span>
                      </div>
                    </>
                  )}
                  
                  {result.error && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Error Code:</span>
                      <span className="font-mono text-red-600">{result.error}</span>
                    </div>
                  )}
                </div>
              </div>
            )}

            {!result && !isLoading && (
              <div className="p-4 bg-gray-50 border border-gray-200 rounded-md">
                <p className="text-gray-600 text-center">
                  Fill in your pCloud credentials and click "Test Connection" to verify your account access.
                </p>
                <div className="mt-3 text-xs text-gray-500">
                  <p><strong>Note:</strong> pCloud has two data centers:</p>
                  <ul className="list-disc list-inside mt-1 space-y-1">
                    <li><strong>US:</strong> api.pcloud.com (default)</li>
                    <li><strong>EU:</strong> eapi.pcloud.com</li>
                  </ul>
                  <p className="mt-2">Select the region where your account was created.</p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* API Information */}
        <div className="mt-6 p-4 bg-gray-50 border border-gray-200 rounded-md">
          <h3 className="text-sm font-medium text-gray-700 mb-2">API Information:</h3>
          <div className="text-xs text-gray-600 space-y-1">
            <p><strong>Endpoint:</strong> https://{config.region === 'eu' ? 'eapi' : 'api'}.pcloud.com/userinfo</p>
            <p><strong>Method:</strong> GET with username/password authentication</p>
            <p><strong>Timeout:</strong> 10 seconds</p>
          </div>
        </div>
      </div>
    </div>
  );
}
