import React, { useState } from 'react';
import { Modal } from '../components/modal';
import { Button } from '../components/button';

export function ModalExamples() {
  const [modals, setModals] = useState({
    small: false,
    medium: false,
    large: false,
    extraLarge: false,
    fullscreen: false,
    form: false,
    confirmation: false
  });

  const openModal = (type: keyof typeof modals) => {
    setModals(prev => ({ ...prev, [type]: true }));
  };

  const closeModal = (type: keyof typeof modals) => {
    setModals(prev => ({ ...prev, [type]: false }));
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">Responsive Modal Examples</h1>
        <p className="text-gray-600">Test different modal sizes and responsive behavior</p>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
        <Button onClick={() => openModal('small')} variant="secondary">
          Small Modal
        </Button>
        
        <Button onClick={() => openModal('medium')} variant="secondary">
          Medium Modal
        </Button>
        
        <Button onClick={() => openModal('large')} variant="secondary">
          Large Modal
        </Button>
        
        <Button onClick={() => openModal('extraLarge')} variant="secondary">
          Extra Large Modal
        </Button>
        
        <Button onClick={() => openModal('fullscreen')} variant="secondary">
          Fullscreen Modal
        </Button>
        
        <Button onClick={() => openModal('form')} variant="primary">
          Form Modal
        </Button>
      </div>

      {/* Small Modal */}
      <Modal
        isOpen={modals.small}
        onClose={() => closeModal('small')}
        size="sm"
      >
        <div className="text-center">
          <h3 className="text-lg font-semibold mb-4">Small Modal</h3>
          <p className="text-gray-600 mb-6">
            This is a small modal perfect for confirmations and simple messages.
          </p>
          <Button onClick={() => closeModal('small')} variant="primary">
            Got it
          </Button>
        </div>
      </Modal>

      {/* Medium Modal */}
      <Modal
        isOpen={modals.medium}
        onClose={() => closeModal('medium')}
        size="md"
      >
        <div>
          <h3 className="text-xl font-semibold mb-4">Medium Modal</h3>
          <p className="text-gray-600 mb-4">
            This is a medium-sized modal suitable for forms and detailed content.
          </p>
          <p className="text-gray-600 mb-6">
            It adapts well to different screen sizes and provides a good balance 
            between content space and screen real estate.
          </p>
          <div className="flex gap-3">
            <Button onClick={() => closeModal('medium')} variant="primary" className="flex-1">
              Continue
            </Button>
            <Button onClick={() => closeModal('medium')} variant="secondary" className="flex-1">
              Cancel
            </Button>
          </div>
        </div>
      </Modal>

      {/* Large Modal */}
      <Modal
        isOpen={modals.large}
        onClose={() => closeModal('large')}
        size="lg"
      >
        <div>
          <h3 className="text-2xl font-semibold mb-6">Large Modal</h3>
          <div className="space-y-4 mb-6">
            <p className="text-gray-600">
              This is a large modal perfect for complex forms, data tables, or detailed content.
            </p>
            <div className="bg-gray-50 p-4 rounded-lg">
              <h4 className="font-medium mb-2">Features:</h4>
              <ul className="list-disc list-inside space-y-1 text-sm text-gray-600">
                <li>Responsive design that adapts to screen size</li>
                <li>Smooth animations with Framer Motion</li>
                <li>Keyboard navigation support (ESC to close)</li>
                <li>Focus management for accessibility</li>
                <li>Click outside to close functionality</li>
              </ul>
            </div>
            <div className="bg-blue-50 p-4 rounded-lg">
              <h4 className="font-medium mb-2">Responsive Behavior:</h4>
              <ul className="list-disc list-inside space-y-1 text-sm text-gray-600">
                <li>Mobile: Full width with padding</li>
                <li>Tablet: Constrained width with margins</li>
                <li>Desktop: Fixed max-width with centering</li>
              </ul>
            </div>
          </div>
          <Button onClick={() => closeModal('large')} variant="primary" className="w-full">
            Close Large Modal
          </Button>
        </div>
      </Modal>

      {/* Extra Large Modal */}
      <Modal
        isOpen={modals.extraLarge}
        onClose={() => closeModal('extraLarge')}
        size="xl"
      >
        <div>
          <h3 className="text-2xl font-semibold mb-6">Extra Large Modal</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
              <h4 className="font-medium mb-3">Left Column</h4>
              <p className="text-gray-600 mb-4">
                Extra large modals are perfect for complex interfaces, dashboards, 
                or when you need to display a lot of information.
              </p>
              <div className="bg-green-50 p-4 rounded-lg">
                <p className="text-sm text-green-800">
                  This modal size is great for data entry forms, settings panels, 
                  or any content that benefits from more horizontal space.
                </p>
              </div>
            </div>
            <div>
              <h4 className="font-medium mb-3">Right Column</h4>
              <div className="space-y-3">
                <div className="bg-gray-100 p-3 rounded">Sample content block 1</div>
                <div className="bg-gray-100 p-3 rounded">Sample content block 2</div>
                <div className="bg-gray-100 p-3 rounded">Sample content block 3</div>
              </div>
            </div>
          </div>
          <Button onClick={() => closeModal('extraLarge')} variant="primary" className="w-full">
            Close Extra Large Modal
          </Button>
        </div>
      </Modal>

      {/* Fullscreen Modal */}
      <Modal
        isOpen={modals.fullscreen}
        onClose={() => closeModal('fullscreen')}
        size="full"
      >
        <div className="h-full flex flex-col">
          <div className="mb-6">
            <h3 className="text-3xl font-semibold mb-2">Fullscreen Modal</h3>
            <p className="text-gray-600">
              This modal takes up the entire viewport, perfect for immersive experiences.
            </p>
          </div>
          
          <div className="flex-1 overflow-y-auto">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {Array.from({ length: 12 }, (_, i) => (
                <div key={i} className="bg-gradient-to-br from-blue-50 to-blue-100 p-6 rounded-lg">
                  <h4 className="font-medium mb-2">Content Block {i + 1}</h4>
                  <p className="text-sm text-gray-600">
                    This is sample content to demonstrate how fullscreen modals 
                    can handle large amounts of content with proper scrolling.
                  </p>
                </div>
              ))}
            </div>
          </div>
          
          <div className="mt-6 pt-6 border-t">
            <Button onClick={() => closeModal('fullscreen')} variant="primary" className="w-full">
              Exit Fullscreen
            </Button>
          </div>
        </div>
      </Modal>

      {/* Form Modal Example */}
      <Modal
        isOpen={modals.form}
        onClose={() => closeModal('form')}
        size="lg"
      >
        <div>
          <h3 className="text-2xl font-semibold mb-6">Responsive Form Modal</h3>
          <form className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  First Name
                </label>
                <input
                  type="text"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter first name"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Last Name
                </label>
                <input
                  type="text"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter last name"
                />
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Email Address
              </label>
              <input
                type="email"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter email address"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Message
              </label>
              <textarea
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter your message"
              />
            </div>
            
            <div className="flex flex-col sm:flex-row gap-3 pt-4">
              <Button type="submit" variant="primary" className="flex-1">
                Submit Form
              </Button>
              <Button 
                type="button" 
                onClick={() => closeModal('form')} 
                variant="secondary" 
                className="flex-1"
              >
                Cancel
              </Button>
            </div>
          </form>
        </div>
      </Modal>
    </div>
  );
}
