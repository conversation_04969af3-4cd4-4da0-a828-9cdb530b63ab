import { Client } from 'pg';

export interface PostgreSQLConnectionConfig {
  host: string;
  port: number;
  database: string;
  user: string;
  password: string;
  ssl?: boolean;
}

// Default database configuration
const DEFAULT_DB_CONFIG: PostgreSQLConnectionConfig = {
  host: 'ep-dawn-fog-a1jk7z7f-pooler.ap-southeast-1.aws.neon.tech',
  port: 5432,
  database: 'neondb',
  user: 'neondb_owner',
  password: 'npg_v1aKnJdNXif4',
  ssl: true,
};

/**
 * Get a database connection with optional custom configuration
 * @param config Optional custom database configuration
 * @returns Promise<Client> PostgreSQL client instance
 */
export const getDbConnection = async (config?: PostgreSQLConnectionConfig): Promise<Client> => {
  const dbConfig = config || DEFAULT_DB_CONFIG;

  const client = new Client({
    host: dbConfig.host,
    port: dbConfig.port,
    database: dbConfig.database,
    user: dbConfig.user,
    password: dbConfig.password,
    ssl: dbConfig.ssl ? { rejectUnauthorized: false } : false,
    connectionTimeoutMillis: 10000,
    query_timeout: 30000,
  });

  await client.connect();
  return client;
};

/**
 * Execute a database query with automatic connection management
 * @param queryText SQL query string
 * @param params Query parameters
 * @param config Optional custom database configuration
 * @returns Promise with query result
 */
export const executeQuery = async (
  queryText: string,
  params: any[] = [],
  config?: PostgreSQLConnectionConfig
) => {
  let client: Client | null = null;

  try {
    client = await getDbConnection(config);
    const result = await client.query(queryText, params);
    return result;
  } finally {
    if (client) {
      await client.end();
    }
  }
};

/**
 * Execute multiple queries in a transaction
 * @param queries Array of query objects with text and params
 * @param config Optional custom database configuration
 * @returns Promise with transaction result
 */
export const executeTransaction = async (
  queries: Array<{ text: string; params?: any[] }>,
  config?: PostgreSQLConnectionConfig
) => {
  let client: Client | null = null;

  try {
    client = await getDbConnection(config);
    await client.query('BEGIN');

    const results = [];
    for (const query of queries) {
      const result = await client.query(query.text, query.params || []);
      results.push(result);
    }

    await client.query('COMMIT');
    return results;
  } catch (error) {
    if (client) {
      await client.query('ROLLBACK');
    }
    throw error;
  } finally {
    if (client) {
      await client.end();
    }
  }
};

/**
 * Test database connection
 * @param config Optional custom database configuration
 * @returns Promise<boolean> Connection success status
 */
export const testConnection = async (config?: PostgreSQLConnectionConfig): Promise<boolean> => {
  let client: Client | null = null;

  try {
    client = await getDbConnection(config);
    await client.query('SELECT 1');
    return true;
  } catch (error) {
    console.error('Database connection test failed:', error);
    return false;
  } finally {
    if (client) {
      await client.end();
    }
  }
};
