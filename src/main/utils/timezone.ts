// Thailand timezone utility functions
export const THAILAND_TIMEZONE = 'Asia/Bangkok';

/**
 * Get current timestamp in Thailand timezone for PostgreSQL queries
 * @returns String representation of current timestamp in Thailand timezone
 */
export function getCurrentThailandTimestamp(): string {
  return `CURRENT_TIMESTAMP AT TIME ZONE 'UTC' AT TIME ZONE '${THAILAND_TIMEZONE}'`;
}

/**
 * Get current timestamp as a JavaScript Date object in Thailand timezone
 * @returns Date object representing current time in Thailand timezone
 */
export function getCurrentThailandDate(): Date {
  return new Date(new Date().toLocaleString("en-US", {timeZone: THAILAND_TIMEZONE}));
}

/**
 * Format a JavaScript Date to Thailand timezone for PostgreSQL
 * @param date JavaScript Date object
 * @returns String representation of the date in Thailand timezone
 */
export function formatThailandTimestamp(date?: Date): string {
  if (!date) return getCurrentThailandTimestamp();
  const thailandDate = new Date(date.toLocaleString("en-US", {timeZone: THAILAND_TIMEZONE}));
  return `'${thailandDate.toISOString()}'::timestamp AT TIME ZONE '${THAILAND_TIMEZONE}'`;
}

/**
 * Convert a PostgreSQL timestamp to Thailand timezone in SELECT queries
 * @param columnName The column name to convert (can include table alias like 'u.last_login')
 * @returns String for use in SELECT clause
 */
export function selectThailandTimestamp(columnName: string): string {
  // Extract just the column name for the alias (remove table prefix if exists)
  const aliasName = columnName.includes('.') ? columnName.split('.').pop() : columnName;
  return `${columnName} AT TIME ZONE '${THAILAND_TIMEZONE}' as ${aliasName}_thai`;
}

/**
 * Get a date range condition for Thailand timezone
 * @param columnName The column name to filter
 * @param hours Number of hours back from current time
 * @returns String for use in WHERE clause
 */
export function getThailandTimeRange(columnName: string, hours: number = 24): string {
  return `${columnName} > (${getCurrentThailandTimestamp()} - INTERVAL '${hours} hours')`;
}

/**
 * Format current date and time for logging in Thailand timezone
 * @returns Formatted string for display/logging
 */
export function getCurrentThailandDateTimeString(): string {
  return new Date().toLocaleString("en-US", {
    timeZone: THAILAND_TIMEZONE,
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  });
}

/**
 * Convert UTC timestamp to Thailand timezone for display
 * @param utcTimestamp UTC timestamp from database
 * @returns Date object in Thailand timezone
 */
export function convertToThailandTime(utcTimestamp: Date): Date {
  return new Date(utcTimestamp.toLocaleString("en-US", {timeZone: THAILAND_TIMEZONE}));
}

/**
 * Get current date string in Thailand timezone (YYYY-MM-DD format)
 */
export function getThailandDateString(date?: Date): string {
  const targetDate = date || new Date();
  return targetDate.toLocaleDateString('sv-SE', { 
    timeZone: THAILAND_TIMEZONE 
  }); // Returns YYYY-MM-DD format
}

/**
 * Get current time string in Thailand timezone (HH:MM:SS format)
 */
export function getThailandTimeString(date?: Date): string {
  const targetDate = date || new Date();
  return targetDate.toLocaleTimeString('en-GB', {
    timeZone: THAILAND_TIMEZONE,
    hour12: false
  }); // Returns HH:MM:SS format
}

/**
 * Get a date range condition for session expiry (1 hour by default)
 * @param columnName The column name to filter
 * @param hours Number of hours for session validity (default: 1)
 * @returns String for use in WHERE clause
 */
export function getSessionExpiryRange(columnName: string, hours: number = 1): string {
  return `${columnName} > (${getCurrentThailandTimestamp()} - INTERVAL '${hours} hours')`;
}

/**
 * Check if a session is expired based on Thailand timezone
 * @param sessionCreateTime The session creation time
 * @param expiryHours Number of hours for session validity (default: 1)
 * @returns boolean indicating if session is expired
 */
export function isSessionExpired(sessionCreateTime: Date, expiryHours: number = 1): boolean {
  const now = getCurrentThailandDate();
  const expiryTime = new Date(sessionCreateTime.getTime() + (expiryHours * 60 * 60 * 1000));
  return now > expiryTime;
}
