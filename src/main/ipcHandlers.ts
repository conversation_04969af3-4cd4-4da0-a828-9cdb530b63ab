// Import all handler modules
import { setupTodoHandlers } from './handler/todoHandler';
import { setupCsvHandlers } from './handler/csvHandler';
import { setupConnectionHandlers } from './handler/connectionHandler';
import { setupBankMasterHandlers } from './handler/bankMasterHandler';
import { setupAuthHandlers } from './handler/authHandler';
import { setupSystemHandlers } from './handler/systemHandler';

export function setupIpcHandlers() {
  console.log('Setting up IPC handlers...');

  // Call all handler setup functions
  setupTodoHandlers();
  setupCsvHandlers();
  setupConnectionHandlers();
  setupBankMasterHandlers();
  setupAuthHandlers();
  setupSystemHandlers();
}